//
//  ManualTransactionView.swift
//  CStory
//
//  Created by NZUE on 2025/7/14.
//

import SwiftData
import SwiftUI

/// 手动记账视图
///
/// 专门处理手动创建交易的所有界面和逻辑，包括交易类型选择、
/// 分类选择、卡片选择、时间设定和数字键盘输入等功能。
struct ManualTransactionView: View {
  // MARK: - Environment and Dependencies
  @Environment(\.modelContext) private var modelContext
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - ViewModels and Bindings
  @StateObject private var viewModel: ManualTransactionVM
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool
  var onTransactionSaved: (() -> Void)? = nil
  var onCloseRequested: (() -> Void)? = nil

  // MARK: - Initialization

  init(
    viewModel: ManualTransactionVM,
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onTransactionSaved: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._viewModel = StateObject(wrappedValue: viewModel)
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onTransactionSaved = onTransactionSaved
    self.onCloseRequested = onCloseRequested
  }

  // MARK: - Animation Namespace
  @Namespace private var transactionTypeAnimation

  // MARK: - State Properties
  @State private var previousControlBarState: ControlBarState = .numericKeypad
  @State private var cardTextAnimationDirection: Edge = .trailing

  // MARK: - Computed Properties
  private var expenseCategories: [TransactionMainCategoryModel] {
    dataManager.expenseCategories
  }

  private var incomeCategories: [TransactionMainCategoryModel] {
    dataManager.incomeCategories
  }

  var body: some View {
    VStack(spacing: 0) {
      // 交易类型选择器 - 从上方滑入
      transactionTypeSelectorView
        .offset(y: showTopContent ? 0 : -UIScreen.main.bounds.height)

      Spacer()

      // 主内容区域 - 从下方滑入
      mainContentView
        .offset(y: showBottomContent ? 0 : UIScreen.main.bounds.height)
    }
    .onChange(of: viewModel.transactionType) { _, newType in
      viewModel.logTransactionTypeChange(newType)
    }
    .sheet(isPresented: $viewModel.isCurrencySheetVisible) {
      CurrencyConversionView(
        baseCurrencyCode: $viewModel.baseCurrencyCode,
        expenseCurrencyCode: $viewModel.expenseCurrencyCode,
        incomeCurrencyCode: $viewModel.incomeCurrencyCode,
        transferCurrencyCode: $viewModel.transferCurrencyCode,
        transactionAmount: $viewModel.transactionAmount,
        isSheetVisible: $viewModel.isCurrencySheetVisible,
        convertCurrencyCode: $viewModel.convertCurrencyCode,
        conversionRate: $viewModel.conversionRate,
        transactionType: viewModel.transactionType
      )
    }
  }

  // MARK: - View Components

  private var transactionTypeSelectorView: some View {
    VStack(spacing: 16) {
      // 交易类型选择器
      HStack(spacing: 8) {
        HStack(spacing: 2) {
          transactionTypeButton(type: .expense, title: "支出")
          transactionTypeButton(type: .income, title: "收入")
        }
        .background(
          ZStack {
            Color.cWhite
            // 动画背景 - 支出和收入之间
            if viewModel.transactionType == .expense || viewModel.transactionType == .income {
              Color.cAccentBlue
                .cornerRadius(14)
                .frame(width: 90, height: 28)
                .offset(x: viewModel.transactionType == .expense ? -45 : 45)
                .matchedGeometryEffect(
                  id: "transactionTypeBackground", in: transactionTypeAnimation)
            }
          }
        )
        .cornerRadius(24)
        .overlay(
          RoundedRectangle(cornerRadius: 24)
            .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
        )

        transactionTypeButton(type: .transfer, title: "转账")
          .background(
            ZStack {
              Color.cWhite
              // 动画背景 - 转账
              if viewModel.transactionType == .transfer {
                Color.cAccentBlue
                  .cornerRadius(14)
                  .frame(width: 88, height: 28)
                  .matchedGeometryEffect(
                    id: "transactionTypeBackground", in: transactionTypeAnimation)
              }
            }
          )
          .cornerRadius(24)
          .overlay(
            RoundedRectangle(cornerRadius: 24)
              .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
          )
      }
      .padding(.horizontal, 16)

      // 类别选择区域
      categoryTabView
    }
  }

  private var categoryTabView: some View {
    TabView(selection: $viewModel.transactionType) {
      ExpenseCategoryView(
        mainCategories: expenseCategories,
        onCategorySelected: viewModel.onExpenseCategorySelected
      )
      .tag(TransactionType.expense)
      IncomeCategoryView(
        mainCategories: incomeCategories,
        onCategorySelected: viewModel.onIncomeCategorySelected
      )
      .tag(TransactionType.income)
      TransferCategoryView(
        cards: dataManager.cards,
        isTransferOutgoing: $viewModel.isTransferOutgoing,
        isTransferCardSelected: $viewModel.isTransferCardSelected,
        controlBarState: $viewModel.controlBarState,
        selectedTransferSourceCardId: $viewModel.selectedTransferSourceCardId,


        selectedTransferDestinationCardId: $viewModel.selectedTransferDestinationCardId
      )
      .tag(TransactionType.transfer)
    }
    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
    .onChange(of: viewModel.transactionType) { oldValue, newValue in
      if oldValue != newValue {
        dataManager.hapticManager.trigger(.selection)
      }
    }
  }

  private var mainContentView: some View {
    VStack(spacing: 16) {
      controlBar

      featureAreaTabView
        .frame(height: 266)
    }
  }

  private var controlBar: some View {
    HStack(spacing: 12) {
      ScrollView(.horizontal, showsIndicators: false) {
        HStack(spacing: 8) {
          if viewModel.transactionType != .transfer {
            cardSelectionButton
          }
          timeSelectionButton
          currencyButton
        }
        .padding(.vertical, 2)
        .padding(.leading, 16)
      }

      // 右侧固定按钮区域
      HStack(spacing: 8) {
        // AI 记账按钮
        Button(action: {
          // 切换到 AI 记账模式的逻辑
          // 这里需要通过通知或回调来通知父组件切换模式
          // 震动反馈由父视图在接收通知时处理
          NotificationCenter.default.post(name: .switchToAIMode, object: nil)
        }) {
          Image(systemName: "apple.intelligence")
            .font(.system(size: 16, weight: .regular))
            .foregroundColor(.cBlack)
            .frame(height: 24)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
            .background(Color.cWhite)
            .cornerRadius(14)
            .overlay(
              RoundedRectangle(cornerRadius: 14)
                .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
            )
        }

        // 关闭按钮
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          // 关闭交易视图的逻辑
          onCloseRequested?()
        }) {
          Image(systemName: "xmark")
            .font(.system(size: 16, weight: .regular))
            .foregroundColor(.cBlack)
            .frame(height: 24)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
            .background(Color.cWhite)
            .cornerRadius(14)
            .overlay(
              RoundedRectangle(cornerRadius: 14)
                .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
            )
        }
      }
      .padding(.trailing, 16)
    }
    .animation(.easeInOut, value: viewModel.transactionType)
  }

  private var cardSelectionButton: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.selection)
      withAnimation {
        viewModel.handleCardSelectionButtonTap()
      }
    }) {
      HStack(spacing: 0) {
        Group {
          if let card = viewModel.selectedCard, let imageData = card.bankLogo,
            let uiImage = UIImage(data: imageData)
          {
            Image(uiImage: uiImage)
              .resizable()
              .frame(width: 24, height: 24)
              .transition(.blurReplace())
          } else {
            Image("wallet_icon")
              .foregroundColor(.cBlack)
              .frame(width: 24, height: 24)
              .transition(.blurReplace())
          }
        }
        .background(Color.cWhite)
        .cornerRadius(12)

        HStack {
          Text(viewModel.cardSelectionButtonText)
            .padding(.leading, 2)
            .padding(.trailing, 8)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)
            .id(viewModel.cardSelectionButtonText)  // 添加 ID 以触发转换
            .transition(
              .asymmetric(
                insertion: .move(edge: cardTextAnimationDirection).combined(with: .opacity),
                removal: .move(edge: cardTextAnimationDirection == .trailing ? .leading : .trailing)
                  .combined(with: .opacity)
              )
            )
        }
        .clipped()  // 在 HStack 边界裁剪，让文字在图标处消失
        .animation(.easeInOut(duration: 0.3), value: viewModel.cardSelectionButtonText)
      }
      .padding(.leading, 2)
      .padding(.vertical, 2)
      .background(viewModel.cardSelectionButtonBackgroundColor)
      .cornerRadius(14)
      .overlay(
        RoundedRectangle(cornerRadius: 14)
          .strokeBorder(viewModel.cardSelectionButtonBorderColor, lineWidth: 1)
      )
    }
  }

  private var timeSelectionButton: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.selection)
      withAnimation {
        viewModel.handleTimeSelectionButtonTap()
      }
    }) {
      HStack(spacing: 4) {
        Image("clock_icon")
          .foregroundColor(.cBlack)
          .frame(width: 24, height: 24)
          .background(Color.cWhite)
          .cornerRadius(12)
        Text(viewModel.formattedTime)
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)
          .contentTransition(
            .numericText(value: viewModel.selectedDate.timeIntervalSinceReferenceDate))
      }
      .padding(.leading, 2)
      .padding(.trailing, 8)
      .padding(.vertical, 2)
      .background(viewModel.timeSelectionButtonBackgroundColor)
      .cornerRadius(14)
      .overlay(
        RoundedRectangle(cornerRadius: 14)
          .strokeBorder(viewModel.timeSelectionButtonBorderColor, lineWidth: 1)
      )
    }
  }

  private var currencyButton: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.selection)
      withAnimation {
        viewModel.handleCurrencyButtonTap()
      }
    }) {
      Text(viewModel.currentCurrencyCode)
        .font(.system(size: 14, weight: .medium))
        .frame(height: 24)
        .padding(.horizontal, 8)
        .padding(.vertical, 2)
        .background(Color.cWhite)
        .foregroundColor(.cBlack)
        .cornerRadius(14)
        .overlay(
          RoundedRectangle(cornerRadius: 14)
            .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
        )
    }
  }

  private var featureAreaTabView: some View {
    TabView(selection: $viewModel.controlBarState) {
      selectCardView.tag(ControlBarState.selectCard)
      numericKeypadView.tag(ControlBarState.numericKeypad)
      selectTimeView.tag(ControlBarState.selectTime)
    }
    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
    .animation(
      .interactiveSpring(response: 0.4, dampingFraction: 0.8), value: viewModel.controlBarState
    )
    .onChange(of: viewModel.controlBarState) { oldValue, newValue in
      // 触发触觉反馈
      dataManager.hapticManager.trigger(.selection)

      // 确定动画方向
      if newValue == .selectCard {
        // 进入选择卡片状态
        cardTextAnimationDirection = .trailing
      } else if oldValue == .selectCard {
        // 从选择卡片状态返回
        cardTextAnimationDirection = .leading
      }

      // 更新前一个状态
      previousControlBarState = oldValue

      // 根据状态变化添加不同的动画效果
      withAnimation(.easeInOut(duration: 0.35)) {
        // 这里可以添加额外的状态更新或动画触发
      }
    }
    .onChange(of: viewModel.selectedCard) { oldValue, newValue in
      // 选中卡片发生变化时的动画方向
      if newValue != nil && oldValue == nil {
        // 从无卡片到有卡片 - 从右侧进入
        cardTextAnimationDirection = .trailing
      } else if newValue == nil && oldValue != nil {
        // 从有卡片到无卡片 - 从左侧进入
        cardTextAnimationDirection = .leading
      } else if newValue != nil && oldValue != nil {
        // 切换卡片 - 从右侧进入
        cardTextAnimationDirection = .trailing
      }
    }
  }

  private var selectCardView: some View {
    VStack(spacing: 12) {
      HStack(spacing: 16) {
        Text(viewModel.selectionTitle)
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.cBlack)
        Spacer()
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          withAnimation {
            viewModel.clearSelection()
          }
        }) {
          Text("不选择卡片")
            .font(.system(size: 14, weight: .medium))
            .frame(height: 24)
            .padding(.horizontal, 8)
            .background(Color.cWhite)
            .cornerRadius(12)
            .foregroundColor(.cBlack)
            .overlay(
              RoundedRectangle(cornerRadius: 12)
                .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
            )
        }
        Button(action: {
          dataManager.hapticManager.trigger(.impactMedium)
          withAnimation {
            viewModel.handleConfirmButtonTap()
          }
        }) {
          Text("确定")
            .font(.system(size: 14, weight: .medium))
            .frame(height: 24)
            .padding(.horizontal, 8)
            .background(Color.cAccentBlue)
            .cornerRadius(12)
            .foregroundColor(.cWhite)
        }
      }
      .padding(.top, 12)

      ScrollView(showsIndicators: false) {
        LazyVStack(spacing: 12) {
          ForEach(dataManager.cards.filter { $0.isSelected }) { card in
            let cardViewModel = CardRowVM(
              from: card,
              isSelected: viewModel.isCardSelected(card),
              showTypeTag: true,
              showAdditionalInfo: false
            )
            CardRow(viewModel: cardViewModel)
              .onAppear {
                cardViewModel.onTap = {
                  withAnimation {
                    viewModel.selectCard(card)
                  }
                }
              }
          }

          // 添加卡片按钮
          AddCardButton(
            viewModel: AddCardButtonVM.standard {
              pathManager.path.append(NavigationDestination.cardCategoryView)
            })
        }
        .padding(.top, 1)
      }
    }
    .padding(.horizontal, 12)
    .background(Color.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 12)
  }

  private var numericKeypadView: some View {
    VStack(spacing: 12) {
      HStack(spacing: 4) {
        if viewModel.transactionType == .expense {
          Button(action: {
            dataManager.hapticManager.trigger(.selection)
            viewModel.handleDiscountButtonTap()
          }) {
            Image("discount_icon")
              .foregroundStyle(
                viewModel.showDiscount ? Color.accentColor : Color.cBlack.opacity(0.2)
              )
              .scaleEffect(viewModel.showDiscount ? 1.1 : 1.0)
              .animation(.easeInOut(duration: 0.2), value: viewModel.showDiscount)
          }
          .buttonStyle(PlainButtonStyle())
        }
        ZStack {
          HStack {
            Text("优惠金额")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)
            Spacer()
            HStack(alignment: .firstTextBaseline, spacing: 2) {
              Text(viewModel.discountExpression)
                .font(.system(size: 32, weight: .medium, design: .monospaced))
                .foregroundColor(.cBlack)
                .lineLimit(1)
                .minimumScaleFactor(0.5)  // 允许缩放到原始大小的50%
                .truncationMode(.head)  // 从头部截断（显示尾部数字更重要）
            }
          }
          .opacity(viewModel.showDiscount && viewModel.transactionType == .expense ? 1 : 0)
          .offset(y: viewModel.showDiscount && viewModel.transactionType == .expense ? 0 : -40)
          .animation(.easeInOut(duration: 0.3), value: viewModel.showDiscount)

          HStack {
            Spacer()
            HStack(alignment: .firstTextBaseline, spacing: 2) {
              Text(viewModel.transactionExpression)
                .font(.system(size: 32, weight: .medium, design: .monospaced))
                .foregroundColor(.cBlack)
                .lineLimit(1)
                .minimumScaleFactor(0.5)  // 允许缩放到原始大小的50%
                .truncationMode(.head)  // 从头部截断（显示尾部数字更重要）
            }
          }
          .opacity(viewModel.showDiscount && viewModel.transactionType == .expense ? 0 : 1)
          .offset(y: viewModel.showDiscount && viewModel.transactionType == .expense ? 40 : 0)
          .animation(.easeInOut(duration: 0.3), value: viewModel.showDiscount)
        }
      }
      .frame(maxWidth: .infinity, minHeight: 42)
      .padding(.horizontal, 16)
      .padding(.vertical, 10)
      .background(Color.cWhite.opacity(0.5))
      .cornerRadius(24)
      .overlay(
        RoundedRectangle(cornerRadius: 24)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )

      NumericKeypad(
        expression: viewModel.showDiscount
          ? $viewModel.discountExpression : $viewModel.transactionExpression,
        transactionAmount: $viewModel.transactionAmount,
        discountAmount: $viewModel.discountAmount,
        onSave: {
          // 获取交易金额和优惠金额
          guard let amount = Double(viewModel.transactionAmount), amount > 0 else {
            return
          }

          let discount = Double(viewModel.discountAmount) ?? 0

          // 使用ViewModel创建交易记录
          let newTransaction = viewModel.createTransaction(amount: amount, discount: discount)

          // 保存到数据库
          do {
            modelContext.insert(newTransaction)
            try modelContext.save()

            // 重新计算相关卡片的余额
            BalanceRecalculationService.shared.recalculateBalances(
              for: [newTransaction.fromCardId, newTransaction.toCardId],
              modelContext: modelContext,
              currencies: dataManager.currencies,
              operation: "交易创建"
            )

            // 保存成功后的处理
            dataManager.hapticManager.trigger(.success)

            // 通知父组件关闭页面
            onTransactionSaved?()
          } catch {
            // 处理保存失败的情况
          }
        },
        onRecord: {
          // 语音记录功能 - 可以后续扩展
        },
        allowNegative: false,  // 交易创建不允许负数
        maxDecimalPlaces: 2  // 普通金额使用2位小数
      )
      .onChange(of: viewModel.transactionAmount) {
        viewModel.handleTransactionAmountChange()
      }
      .onChange(of: viewModel.transactionExpression) {
        viewModel.handleTransactionExpressionChange()
      }
      .onChange(of: viewModel.discountExpression) {
        viewModel.handleDiscountExpressionChange()
      }
      .onChange(of: viewModel.transactionType) {
        viewModel.handleTransactionTypeChangeForDiscount()
      }
    }
    .padding(.horizontal, 12)
  }

  private var selectTimeView: some View {
    VStack(spacing: 12) {
      HStack(spacing: 16) {
        Text(viewModel.formattedDate)
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.cBlack)
          .contentTransition(
            .numericText(value: viewModel.selectedDate.timeIntervalSinceReferenceDate))
        Spacer()
        Button(action: {
          dataManager.hapticManager.trigger(.selection)
          withAnimation {
            viewModel.handleBackToTodayButtonTap()
          }
        }) {
          Text("回到今日")
            .font(.system(size: 14, weight: .medium))
            .frame(height: 24)
            .padding(.horizontal, 8)
            .background(Color.cWhite)
            .cornerRadius(12)
            .foregroundColor(.cBlack)
            .overlay(
              RoundedRectangle(cornerRadius: 12)
                .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
            )
        }
        Button(action: {
          dataManager.hapticManager.trigger(.impactMedium)
          withAnimation {
            viewModel.handleConfirmButtonTap()
          }
        }) {
          Text("确定")
            .font(.system(size: 14, weight: .medium))
            .frame(height: 24)
            .padding(.horizontal, 8)
            .background(Color.cAccentBlue)
            .cornerRadius(12)
            .foregroundColor(.cWhite)
        }
      }
      .padding(.top, 12)

      DatePicker(
        "", selection: $viewModel.selectedDate, displayedComponents: [.date, .hourAndMinute]
      )
      #if os(iOS)
        .datePickerStyle(WheelDatePickerStyle())
        .transformEffect(.init(scaleX: 0.95, y: 0.95))
      #else
        .datePickerStyle(.stepperField)
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 24)
      #endif
      .labelsHidden()
    }
    .padding(.horizontal, 12)
    .background(Color.cWhite.opacity(0.5))
    .cornerRadius(24)
    .overlay(
      RoundedRectangle(cornerRadius: 24)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 12)
  }

  // MARK: - Helper Methods

  private func transactionTypeButton(type: TransactionType, title: String) -> some View {
    Button(action: {
      dataManager.hapticManager.trigger(.selection)
      withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
        viewModel.handleTransactionTypeButtonTap(type)
      }
    }) {
      Text(title)
        .font(.system(size: 16, weight: .medium))
        .frame(width: 88, height: 28)
        .background(Color.clear)
        .foregroundColor(viewModel.transactionTypeButtonForegroundColor(for: type))
        .cornerRadius(14)
        .padding(2)
    }
  }

}

#Preview {
  @Previewable @State var showTopContent = true
  @Previewable @State var showBottomContent = true
  @Previewable @State var hapticTrigger = false

  ManualTransactionView(
    viewModel: ManualTransactionVM(dataManager: DataManagement()),
    showTopContent: $showTopContent,
    showBottomContent: $showBottomContent,
    onTransactionSaved: {
      // Transaction saved callback
    },
    onCloseRequested: {
      // Close requested callback
    }
  )
  .environment(
    \.dataManager,
    DataManagement(
      cards: [],
      mainCategories: [],
      subCategories: [],
      currencies: [],
      recentTransactions: [],
      allTransactions: []
    )
  )
  .modelContainer(try! ModelContainer(for: TransactionModel.self))
}
