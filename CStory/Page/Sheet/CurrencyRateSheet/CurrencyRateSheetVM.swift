//
//  CurrencyRateSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/1.
//

import SwiftData
import SwiftUI

/// 货币汇率设置Sheet的ViewModel
final class CurrencyRateSheetVM: ObservableObject {

  // MARK: - Published Properties

  /// 自定义汇率输入文本
  @Published var customRateText: String = ""

  /// 是否显示键盘
  @Published var showKeyboard: Bool = false

  /// 显示恢复默认汇率确认弹窗
  @Published var showRestoreAlert: Bool = false

  // MARK: - Private Properties

  /// 当前操作的货币
  private var currency: CurrencyModel?

  // MARK: - Initialization

  init(currency: CurrencyModel? = nil) {
    if let currency = currency {
      self.currency = currency
      // 初始化显示的自定义汇率
      if let customRate = currency.customRate {
        customRateText = formatRateWithSixDecimals(customRate)
      } else {
        customRateText = ""
      }
    }
  }

  // MARK: - Public Methods

  /// 配置ViewModel
  /// - Parameter currency: 要操作的货币模型
  func configure(with currency: CurrencyModel) {
    self.currency = currency

    // 强制触发 UI 更新
    objectWillChange.send()

    // 初始化显示的自定义汇率
    if let customRate = currency.customRate {
      customRateText = formatRateWithSixDecimals(customRate)
    } else {
      customRateText = ""
    }
  }

  /// 处理自定义汇率输入点击
  func handleRateInputTap() {
    guard let currency = currency else { return }

    // 初始化自定义汇率文本
    if customRateText.isEmpty {
      // 优先使用当前汇率，如果为0则使用默认汇率
      let displayRate = currency.rate > 0 ? currency.rate : currency.defaultRate
      customRateText = formatRateWithSixDecimals(displayRate)
    }
    showKeyboard = true
  }

  /// 保存自定义汇率
  /// - Parameters:
  ///   - modelContext: SwiftData上下文
  ///   - onSuccess: 成功回调
  func saveCustomRate(using modelContext: ModelContext, onSuccess: @escaping () -> Void) {
    guard let currency = currency,
      !currency.isBaseCurrency,
      let newRate = Double(customRateText)
    else { return }

    // 确保汇率有效
    guard newRate > 0 else {
      customRateText = formatRateWithSixDecimals(currency.rate)
      return
    }

    // 更新汇率
    currency.customRate = newRate
    currency.rate = newRate
    currency.isCustom = true
    currency.updatedAt = Date()

    // 保存更改
    try? modelContext.save()

    // 关闭键盘和执行回调
    showKeyboard = false
    onSuccess()
  }

  /// 恢复默认汇率
  /// - Parameters:
  ///   - modelContext: SwiftData上下文
  ///   - onSuccess: 成功回调
  func restoreDefaultRate(using modelContext: ModelContext, onSuccess: @escaping () -> Void) {
    guard let currency = currency else { return }

    currency.restoreDefaultRate()
    try? modelContext.save()
    onSuccess()
  }

  /// 显示恢复默认汇率确认弹窗
  func showRestoreConfirmation() {
    showRestoreAlert = true
  }

  // MARK: - Computed Properties

  /// 是否可以保存自定义汇率
  var canSaveCustomRate: Bool {
    guard let currency = currency else { return false }
    return !currency.isBaseCurrency && !customRateText.isEmpty
  }

  /// 是否可以恢复默认汇率
  var canRestoreDefault: Bool {
    guard let currency = currency else { return false }
    return !currency.isBaseCurrency && currency.isCustom
  }

  /// 当前汇率格式化文本
  var currentRateText: String {
    guard let currency = currency else {
      return "--"  // 返回占位符而不是 0.000000
    }

    // 优先显示当前汇率，如果为0则显示默认汇率
    let displayRate = currency.rate > 0 ? currency.rate : currency.defaultRate
    return formatRateWithSixDecimals(displayRate)
  }

  // MARK: - Private Methods

  /// 格式化汇率为6位小数，并去除尾随0
  /// - Parameter rate: 汇率值
  /// - Returns: 格式化后的字符串
  private func formatRateWithSixDecimals(_ rate: Double) -> String {
    return NumberFormatService.shared.formatExchangeRate(rate, maxDecimals: 6)
  }
}
